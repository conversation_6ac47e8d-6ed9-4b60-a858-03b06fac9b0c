"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Switch } from "@/components/ui/switch";
import { Settings, <PERSON>ie } from "lucide-react";

interface CookiePreferences {
  necessary: boolean;
  analytics: boolean;
  marketing: boolean;
  functional: boolean;
}

export default function CookiePreferences() {
  const [showBanner, setShowBanner] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [preferences, setPreferences] = useState<CookiePreferences>({
    necessary: true, // Always true, cannot be disabled
    analytics: false,
    marketing: false,
    functional: false,
  });

  // Check if user has made a choice
  useEffect(() => {
    const hasConsent = localStorage.getItem("admesh-cookie-consent");
    if (!hasConsent) {
      // Show banner after a short delay
      const timer = setTimeout(() => setShowBanner(true), 2000);
      return () => clearTimeout(timer);
    } else {
      // Load saved preferences
      const savedPreferences = localStorage.getItem("admesh-cookie-preferences");
      if (savedPreferences) {
        setPreferences(JSON.parse(savedPreferences));
      }
    }
  }, []);

  const savePreferences = (prefs: CookiePreferences) => {
    localStorage.setItem("admesh-cookie-consent", "true");
    localStorage.setItem("admesh-cookie-preferences", JSON.stringify(prefs));
    setPreferences(prefs);
    setShowBanner(false);
    setShowModal(false);
    
    // Apply preferences (you can extend this to actually control cookies)
    if (prefs.analytics) {
      // Enable analytics cookies
      console.log("Analytics cookies enabled");
    }
    if (prefs.marketing) {
      // Enable marketing cookies
      console.log("Marketing cookies enabled");
    }
    if (prefs.functional) {
      // Enable functional cookies
      console.log("Functional cookies enabled");
    }
  };

  const acceptAll = () => {
    const allAccepted = {
      necessary: true,
      analytics: true,
      marketing: true,
      functional: true,
    };
    savePreferences(allAccepted);
  };

  const acceptNecessaryOnly = () => {
    const necessaryOnly = {
      necessary: true,
      analytics: false,
      marketing: false,
      functional: false,
    };
    savePreferences(necessaryOnly);
  };

  const openPreferencesModal = () => {
    setShowModal(true);
    setShowBanner(false);
  };

  const handlePreferenceChange = (key: keyof CookiePreferences, value: boolean) => {
    if (key === "necessary") return; // Cannot disable necessary cookies
    setPreferences(prev => ({ ...prev, [key]: value }));
  };

  const saveCustomPreferences = () => {
    savePreferences(preferences);
  };

  // Cookie banner
  if (showBanner) {
    return (
      <div className="fixed bottom-4 right-4 z-50 max-w-sm">
        <Card className="border-2 border-gray-200 shadow-xl bg-white">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Cookie className="w-5 h-5" />
              Cookie Preferences
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-gray-600">
              We use cookies to enhance your experience and analyze our traffic. 
              Choose your preferences below.
            </p>
            <div className="flex flex-col gap-2">
              <Button
                onClick={acceptAll}
                className="w-full bg-black hover:bg-gray-800 text-white"
              >
                Accept All
              </Button>
              <Button
                onClick={openPreferencesModal}
                variant="outline"
                className="w-full"
              >
                <Settings className="w-4 h-4 mr-2" />
                Change Preferences
              </Button>
              <Button
                onClick={acceptNecessaryOnly}
                variant="ghost"
                className="w-full text-sm"
              >
                Necessary Only
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Preferences modal
  return (
    <>
      {/* Floating preferences button for users who have already made a choice */}
      {!showBanner && (
        <div className="fixed bottom-4 right-4 z-40">
          <Button
            onClick={() => setShowModal(true)}
            variant="outline"
            size="sm"
            className="bg-white shadow-lg border-gray-200 hover:bg-gray-50"
          >
            <Settings className="w-4 h-4 mr-2" />
            Cookie Preferences
          </Button>
        </div>
      )}

      <Dialog open={showModal} onOpenChange={setShowModal}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Cookie className="w-5 h-5" />
              Cookie Preferences
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-6">
            <p className="text-sm text-gray-600">
              Manage your cookie preferences. You can change these settings at any time.
            </p>

            <div className="space-y-4">
              {/* Necessary Cookies */}
              <div className="flex items-start justify-between p-4 border rounded-lg bg-gray-50">
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">Necessary Cookies</h3>
                  <p className="text-sm text-gray-600 mt-1">
                    Essential for the website to function properly. These cannot be disabled.
                  </p>
                </div>
                <Switch
                  checked={true}
                  disabled={true}
                  className="ml-4"
                />
              </div>

              {/* Analytics Cookies */}
              <div className="flex items-start justify-between p-4 border rounded-lg">
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">Analytics Cookies</h3>
                  <p className="text-sm text-gray-600 mt-1">
                    Help us understand how visitors interact with our website by collecting anonymous information.
                  </p>
                </div>
                <Switch
                  checked={preferences.analytics}
                  onCheckedChange={(checked) => handlePreferenceChange("analytics", checked)}
                  className="ml-4"
                />
              </div>

              {/* Functional Cookies */}
              <div className="flex items-start justify-between p-4 border rounded-lg">
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">Functional Cookies</h3>
                  <p className="text-sm text-gray-600 mt-1">
                    Enable enhanced functionality and personalization, such as remembering your preferences.
                  </p>
                </div>
                <Switch
                  checked={preferences.functional}
                  onCheckedChange={(checked) => handlePreferenceChange("functional", checked)}
                  className="ml-4"
                />
              </div>

              {/* Marketing Cookies */}
              <div className="flex items-start justify-between p-4 border rounded-lg">
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">Marketing Cookies</h3>
                  <p className="text-sm text-gray-600 mt-1">
                    Used to track visitors across websites to display relevant advertisements.
                  </p>
                </div>
                <Switch
                  checked={preferences.marketing}
                  onCheckedChange={(checked) => handlePreferenceChange("marketing", checked)}
                  className="ml-4"
                />
              </div>
            </div>

            <div className="flex gap-3 pt-4">
              <Button
                onClick={saveCustomPreferences}
                className="flex-1 bg-black hover:bg-gray-800 text-white"
              >
                Save Preferences
              </Button>
              <Button
                onClick={acceptAll}
                variant="outline"
                className="flex-1"
              >
                Accept All
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
