"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { useAuth } from "@/hooks/use-auth";
import {
  Brain,
  Search,
  Target,
  TrendingUp,
  Sparkles,
  CheckCircle,
  ArrowRight,
  Globe,
  BarChart3
} from "lucide-react";

export default function FreeSEOCheckSection() {
  const { user } = useAuth();
  const { ref: sectionRef, inView: sectionInView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const handleGEOCheck = () => {
    if (user) {
      // User is logged in, redirect to GEO check page
      window.location.href = "/dashboard/brand/geo-check";
    } else {
      // User is not logged in, redirect to sign-in with brand role
      window.location.href = "/auth/signin?role=brand&redirect=/dashboard/brand/geo-check";
    }
  };

  const geoFeatures = [
    {
      icon: <Brain className="w-6 h-6 text-blue-600" />,
      title: "AI Citation Analysis",
      description: "See how often your content gets cited by AI engines like ChatGPT and Claude"
    },
    {
      icon: <Search className="w-6 h-6 text-green-600" />,
      title: "Content Optimization Score",
      description: "Get actionable insights on making your content more AI-friendly"
    },
    {
      icon: <Target className="w-6 h-6 text-purple-600" />,
      title: "Competitive Positioning",
      description: "See how you rank against competitors in AI-generated responses"
    },
    {
      icon: <TrendingUp className="w-6 h-6 text-orange-600" />,
      title: "GEO Recommendations",
      description: "Receive specific recommendations to improve your AI search visibility"
    }
  ];

  const benefits = [
    "Analyze your website&apos;s AI search readiness",
    "Get a comprehensive GEO score (0-100)",
    "Receive actionable optimization recommendations",
    "See how AI engines perceive your content",
    "Compare against industry benchmarks"
  ];

  return (
    <section 
      id="free-seo-check" 
      ref={sectionRef}
      className="w-full py-20 bg-gradient-to-br from-blue-50 via-white to-purple-50"
    >
      <div className="max-w-6xl mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{
            opacity: sectionInView ? 1 : 0,
            y: sectionInView ? 0 : 30,
          }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <span className="inline-flex items-center gap-2 mb-4 px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-medium">
            <Sparkles className="w-4 h-4" />
            Free GEO Analysis
          </span>
          <h2 className="text-3xl md:text-4xl font-bold text-black mb-6">
            Get Your Free Generative Engine Optimization (GEO) Check
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-8">
            Discover how well your website performs in AI-powered search engines.
            Get a comprehensive analysis of your content&apos;s AI readiness and actionable
            recommendations to improve your visibility in AI-generated responses.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
          {/* Left side - GEO Check Form */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            animate={{
              opacity: sectionInView ? 1 : 0,
              x: sectionInView ? 0 : -30,
            }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card className="border-2 border-blue-200 shadow-xl">
              <CardHeader className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-t-lg">
                <CardTitle className="flex items-center gap-2 text-xl">
                  <Globe className="w-6 h-6" />
                  Free GEO Analysis Tool
                </CardTitle>
              </CardHeader>
              <CardContent className="p-8">
                <div className="space-y-6">
                  <div className="text-center">
                    <p className="text-gray-600 mb-6 leading-relaxed">
                      Ready to see how your website performs in AI-powered search engines?
                      Get a comprehensive analysis of your content&apos;s AI readiness and
                      actionable recommendations to improve your visibility.
                    </p>

                    <Button
                      onClick={handleGEOCheck}
                      className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium py-4 text-lg"
                    >
                      <div className="flex items-center gap-2">
                        <BarChart3 className="w-5 h-5" />
                        {user ? "Start GEO Analysis" : "Get Free GEO Analysis"}
                        <ArrowRight className="w-5 h-5" />
                      </div>
                    </Button>
                  </div>

                  <div className="space-y-3">
                    <p className="text-sm font-medium text-gray-700">What you&apos;ll get:</p>
                    {benefits.map((benefit, index) => (
                      <div key={index} className="flex items-start gap-2">
                        <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-gray-600">{benefit}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Right side - Features */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            animate={{
              opacity: sectionInView ? 1 : 0,
              x: sectionInView ? 0 : 30,
            }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="space-y-6"
          >
            <div className="mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                Why GEO Matters More Than Ever
              </h3>
              <p className="text-gray-600 leading-relaxed">
                As AI-powered search engines like ChatGPT, Claude, and Perplexity become the primary way
                users discover information, traditional SEO is evolving. GEO focuses on optimizing your
                content to be cited and recommended by AI engines, not just ranked by search algorithms.
              </p>
            </div>

            <div className="grid grid-cols-1 gap-4">
              {geoFeatures.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{
                    opacity: sectionInView ? 1 : 0,
                    y: sectionInView ? 0 : 20,
                  }}
                  transition={{ duration: 0.5, delay: 0.6 + (index * 0.1) }}
                  className="flex items-start gap-4 p-4 bg-white rounded-lg border border-gray-200 hover:shadow-md transition-shadow"
                >
                  <div className="flex-shrink-0">
                    {feature.icon}
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">{feature.title}</h4>
                    <p className="text-sm text-gray-600">{feature.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
