"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { useAuth } from "@/hooks/use-auth";
import {
  Brain,
  Search,
  Target,
  TrendingUp,
  Sparkles,
  CheckCircle,
  ArrowRight,
  Globe,
  BarChart3
} from "lucide-react";

export default function FreeSEOCheckSection() {
  const { user } = useAuth();
  const { ref: sectionRef, inView: sectionInView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const handleGEOCheck = () => {
    if (user) {
      // User is logged in, redirect to GEO check page
      window.location.href = "/dashboard/brand/geo-check";
    } else {
      // User is not logged in, redirect to sign-in with brand role
      window.location.href = "/auth/signin?role=brand&redirect=/dashboard/brand/geo-check";
    }
  };

  const geoFeatures = [
    {
      icon: <Brain className="w-6 h-6 text-black" />,
      title: "AI Citation Analysis",
      description: "Track AI engine citations and mentions"
    },
    {
      icon: <Search className="w-6 h-6 text-black" />,
      title: "Content Optimization",
      description: "AI-friendly content scoring and insights"
    },
    {
      icon: <Target className="w-6 h-6 text-black" />,
      title: "Competitive Analysis",
      description: "Compare against competitor AI visibility"
    },
    {
      icon: <TrendingUp className="w-6 h-6 text-black" />,
      title: "GEO Recommendations",
      description: "Actionable steps to improve AI rankings"
    }
  ];

  const benefits = [
    "AI readiness score (0-100)",
    "Content optimization tips",
    "Competitive benchmarking",
    "Citation tracking insights",
    "Actionable improvement plan"
  ];

  return (
    <section
      id="free-seo-check"
      ref={sectionRef}
      className="w-full py-20 bg-gray-50"
    >
      <div className="max-w-6xl mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{
            opacity: sectionInView ? 1 : 0,
            y: sectionInView ? 0 : 30,
          }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <span className="inline-flex items-center gap-2 mb-4 px-4 py-2 bg-gray-100 text-gray-800 rounded-full text-sm font-medium">
            <Sparkles className="w-4 h-4" />
            Free GEO Analysis
          </span>
          <h2 className="text-3xl md:text-4xl font-bold text-black mb-6">
            Free GEO Analysis
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-8">
            See how your website performs in AI search engines and get actionable recommendations.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
          {/* Left side - GEO Check Form */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            animate={{
              opacity: sectionInView ? 1 : 0,
              x: sectionInView ? 0 : -30,
            }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card className="border-2 border-gray-200 shadow-xl">
              <CardHeader className="bg-black text-white rounded-t-lg">
                <CardTitle className="flex items-center gap-2 text-xl">
                  <Globe className="w-6 h-6" />
                  Free GEO Analysis Tool
                </CardTitle>
              </CardHeader>
              <CardContent className="p-8">
                <div className="space-y-6">
                  <div className="text-center">
                    <p className="text-gray-600 mb-6">
                      Analyze your website&apos;s AI search performance and get optimization recommendations.
                    </p>

                    <Button
                      onClick={handleGEOCheck}
                      className="w-full bg-black hover:bg-gray-800 text-white font-medium py-4 text-lg"
                    >
                      <div className="flex items-center gap-2">
                        <BarChart3 className="w-5 h-5" />
                        {user ? "Start GEO Analysis" : "Get Free GEO Analysis"}
                        <ArrowRight className="w-5 h-5" />
                      </div>
                    </Button>
                  </div>

                  <div className="space-y-3">
                    <p className="text-sm font-medium text-gray-700">What you&apos;ll get:</p>
                    {benefits.map((benefit, index) => (
                      <div key={index} className="flex items-start gap-2">
                        <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-gray-600">{benefit}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Right side - Features */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            animate={{
              opacity: sectionInView ? 1 : 0,
              x: sectionInView ? 0 : 30,
            }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="space-y-6"
          >
            <div className="mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                Why GEO Matters
              </h3>
              <p className="text-gray-600 leading-relaxed">
                AI engines like ChatGPT and Claude are changing how users find information.
                GEO optimizes your content for AI citations, not just search rankings.
              </p>
            </div>

            <div className="grid grid-cols-1 gap-4">
              {geoFeatures.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{
                    opacity: sectionInView ? 1 : 0,
                    y: sectionInView ? 0 : 20,
                  }}
                  transition={{ duration: 0.5, delay: 0.6 + (index * 0.1) }}
                  className="flex items-start gap-4 p-4 bg-white rounded-lg border border-gray-200 hover:shadow-md transition-shadow"
                >
                  <div className="flex-shrink-0">
                    {feature.icon}
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">{feature.title}</h4>
                    <p className="text-sm text-gray-600">{feature.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
